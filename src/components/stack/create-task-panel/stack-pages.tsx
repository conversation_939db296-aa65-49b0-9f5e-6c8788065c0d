import { StackRef } from "@/components/ui/stack";
import { RefObject } from "react";
import { OnlineStack } from "./online-stack";
import { UploadStack } from "./upload-stack";
import { SubtitlePromptStack } from "./subtitle-prompt-stack";
import { SubtitleEditorStack } from "./subtitle-editor-stack";

export type CreateTaskStackPage = "upload" | "online" | "subtitle-prompt" | "subtitle-editor";

export const createTaskStackPages = {
  upload: (stackRef: RefObject<StackRef>) => (
    <UploadStack stackRef={stackRef} />
  ),
  online: (stackRef: RefObject<StackRef>) => (
    <OnlineStack stackRef={stackRef} />
  ),
  "subtitle-prompt": (stackRef: RefObject<StackRef>) => (
    <SubtitlePromptStack stackRef={stackRef} />
  ),
  "subtitle-editor": (stackRef: RefObject<StackRef>) => (
    <SubtitleEditorStack stackRef={stackRef} />
  ),
} as const;
