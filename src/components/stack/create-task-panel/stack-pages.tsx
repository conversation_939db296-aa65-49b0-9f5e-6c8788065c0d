import { StackRef } from "@/components/ui/stack";
import { RefObject } from "react";
import { OnlineStack } from "./online-stack";
import { UploadStack } from "./upload-stack";
import { SubtitlePromptStack } from "./subtitle-prompt-stack";
import { SubtitleEditorStack } from "./subtitle-editor-stack";
import { SubtitleEditorWithVideoStack } from "./subtitle-editor-with-video-stack";

export type CreateTaskStackPage = "upload" | "online" | "subtitle-prompt" | "subtitle-editor" | "subtitle-editor-with-video";

export const createTaskStackPages = {
  upload: (stackRef: RefObject<StackRef>) => (
    <UploadStack stackRef={stackRef} />
  ),
  online: (stackRef: RefObject<StackRef>) => (
    <OnlineStack stackRef={stackRef} />
  ),
  "subtitle-prompt": (stackRef: RefObject<StackRef>) => (
    <SubtitlePromptStack stackRef={stackRef} />
  ),
  "subtitle-editor": (stackRef: RefObject<StackRef>) => (
    <SubtitleEditorStack stackRef={stackRef} />
  ),
  "subtitle-editor-with-video": (stackRef: RefObject<StackRef>) => (
    <SubtitleEditorWithVideoStack stackRef={stackRef} />
  ),
} as const;
