"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { StackRef } from "@/components/ui/stack";
import { ArrowLef<PERSON>, Loader2, MessageSquare, Wand2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { RefObject, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAtom, useSetAtom } from "jotai";
import { createTaskStackAtom } from "@/stores/slices/stack_store";
import { subtitleDataAtom } from "@/stores/slices/subtitle_store";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

const subtitlePromptSchema = z.object({
  prompt: z.string().min(10, "Prompt must be at least 10 characters long"),
});

type SubtitlePromptFormData = z.infer<typeof subtitlePromptSchema>;

interface SubtitlePromptStackProps {
  stackRef: RefObject<StackRef>;
}

export const SubtitlePromptStack = ({ stackRef }: SubtitlePromptStackProps) => {
  const t = useTranslations();
  const [isGenerating, setIsGenerating] = useState(false);
  const setCurrentStack = useSetAtom(createTaskStackAtom);
  const [subtitleData, setSubtitleData] = useAtom(subtitleDataAtom);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<SubtitlePromptFormData>({
    resolver: zodResolver(subtitlePromptSchema),
    defaultValues: {
      prompt: "",
    },
  });

  const promptValue = watch("prompt");

  const handleBack = () => {
    stackRef.current?.pop();
  };

  const handleGenerateSubtitles = async (data: SubtitlePromptFormData) => {
    setIsGenerating(true);
    
    try {
      // Simulate AI subtitle generation
      // In a real implementation, this would call an AI service
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock generated subtitles
      const mockSubtitles = [
        {
          id: "1",
          startTime: "00:00:00,000",
          endTime: "00:00:03,000",
          text: "Welcome to our cooking tutorial",
          translatedText: "",
        },
        {
          id: "2", 
          startTime: "00:00:03,000",
          endTime: "00:00:06,000",
          text: "Today we'll be making delicious pasta",
          translatedText: "",
        },
        {
          id: "3",
          startTime: "00:00:06,000", 
          endTime: "00:00:09,000",
          text: "First, let's gather our ingredients",
          translatedText: "",
        },
      ];

      setSubtitleData({
        prompt: data.prompt,
        originalSubtitles: mockSubtitles,
        translatedSubtitles: mockSubtitles.map(sub => ({ ...sub, translatedText: "" })),
      });

      toast.success("Subtitles generated successfully!");

      // Navigate to subtitle editor with video
      stackRef.current?.push("subtitle-editor-with-video");
      
    } catch (error) {
      console.error("Error generating subtitles:", error);
      toast.error("Failed to generate subtitles. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex h-full flex-col">
      {/* Header */}
      <div className="flex items-center gap-3 border-b bg-card/50 p-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="h-7 w-7 p-0"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex items-center gap-2">
          <MessageSquare className="h-4 w-4 text-primary" />
          <h2 className="text-sm font-medium">
            {t("form.fields.subtitlePrompt.title")}
          </h2>
        </div>
      </div>

      {/* Content */}
      <ScrollArea className="flex-1">
        <div className="p-6 space-y-6">
          <div className="text-center space-y-2">
            <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <Wand2 className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-lg font-semibold">
              {t("form.fields.subtitlePrompt.title")}
            </h3>
            <p className="text-sm text-muted-foreground max-w-md mx-auto">
              Describe what kind of subtitles you want to create and our AI will generate them for you.
            </p>
          </div>

          <form onSubmit={handleSubmit(handleGenerateSubtitles)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="prompt">
                {t("form.fields.subtitlePrompt.label")}
              </Label>
              <Textarea
                id="prompt"
                placeholder={t("form.fields.subtitlePrompt.placeholder")}
                className={cn(
                  "min-h-[120px] resize-none",
                  errors.prompt && "border-destructive focus-visible:ring-destructive/30"
                )}
                {...register("prompt")}
              />
              {errors.prompt && (
                <p className="text-sm text-destructive">
                  {errors.prompt.message}
                </p>
              )}
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Minimum 10 characters</span>
                <span>{promptValue?.length || 0} characters</span>
              </div>
            </div>

            <Button
              type="submit"
              disabled={isGenerating || !promptValue || promptValue.length < 10}
              className="w-full"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Wand2 className="mr-2 h-4 w-4" />
                  {t("form.fields.subtitlePrompt.generateButton")}
                </>
              )}
            </Button>
          </form>

          {/* Example prompts */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground">
              Example prompts:
            </h4>
            <div className="grid gap-2">
              {[
                "Create subtitles for a cooking tutorial about making pasta",
                "Generate subtitles for a tech product review video",
                "Make subtitles for a travel vlog in Paris",
                "Create educational subtitles for a science experiment",
              ].map((example, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => {
                    const textarea = document.getElementById("prompt") as HTMLTextAreaElement;
                    if (textarea) {
                      textarea.value = example;
                      textarea.dispatchEvent(new Event("input", { bubbles: true }));
                    }
                  }}
                  className="text-left p-3 rounded-md border border-dashed border-muted-foreground/30 hover:border-primary/50 hover:bg-primary/5 transition-colors text-sm text-muted-foreground hover:text-foreground"
                >
                  "{example}"
                </button>
              ))}
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
};
