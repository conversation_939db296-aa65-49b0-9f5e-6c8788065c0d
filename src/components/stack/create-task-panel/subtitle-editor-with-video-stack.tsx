"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { StackRef } from "@/components/ui/stack";
import { ArrowLeft, Edit3, Plus, Download, Trash2, Wand2, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { RefObject, useState, useEffect, useRef, useMemo } from "react";

import { useAtomValue, useSetAtom } from "jotai";
import {
  subtitleDataAtom,
  addSubtitleAtom,
  updateBothSubtitlesAtom,
  deleteSubtitleAtom,
  SubtitleItem
} from "@/stores/slices/subtitle_store";
import { currentTaskAtom } from "@/stores/slices/current_task";
import { ArtPlayer } from "@/components/common/art-player";
import { useVideoInfo } from "@/hooks/swr/use-video-info";
import { isYoutubeUrl, getYoutubeVideoId } from "@/utils/video-format";
import Artplayer from "artplayer";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { toast } from "sonner";



// Helper function to parse SRT content
function parseSRTContent(srtContent: string): SubtitleItem[] {
  const subtitles: SubtitleItem[] = [];

  // Split by double newlines to separate subtitle blocks
  const blocks = srtContent.trim().split(/\n\s*\n/);

  for (const block of blocks) {
    const lines = block.trim().split('\n');

    if (lines.length >= 3) {
      // First line should be the subtitle number
      const numberMatch = lines[0].match(/^\d+$/);
      if (!numberMatch) continue;

      // Second line should be the timing
      const timingMatch = lines[1].match(/^(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})$/);
      if (!timingMatch) continue;

      const startTime = timingMatch[1];
      const endTime = timingMatch[2];

      // Remaining lines are the subtitle text
      const textLines = lines.slice(2);

      // First line is original text, second line is translated text (if present)
      const originalText = textLines[0] || '';
      const translatedText = textLines[1] || '';

      subtitles.push({
        id: numberMatch[0],
        startTime,
        endTime,
        text: originalText,
        translatedText: translatedText
      });
    }
  }

  return subtitles;
}

interface SubtitleEditorWithVideoStackProps {
  stackRef: RefObject<StackRef>;
}

interface SubtitleRowProps {
  subtitle: SubtitleItem;
  editingField: string | null;
  onFieldEdit: (field: string | null) => void;
  onSave: (updates: Partial<SubtitleItem>) => void;
  onDelete: () => void;
  isActive: boolean;
  onSeekTo: (time: number) => void;
}

// Convert time string to seconds
const timeStringToSeconds = (timeString: string): number => {
  const [time, milliseconds] = timeString.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds + (parseInt(milliseconds || '0') / 1000);
};

// Convert seconds to time string
const secondsToTimeString = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
};

const SubtitleRow = ({ 
  subtitle, 
  editingField, 
  onFieldEdit, 
  onSave, 
  onDelete,
  isActive,
  onSeekTo
}: SubtitleRowProps) => {
  const [editData, setEditData] = useState({
    startTime: subtitle.startTime,
    endTime: subtitle.endTime,
    text: subtitle.text,
    translatedText: subtitle.translatedText || "",
  });

  // Update edit data when subtitle changes
  useEffect(() => {
    setEditData({
      startTime: subtitle.startTime,
      endTime: subtitle.endTime,
      text: subtitle.text,
      translatedText: subtitle.translatedText || "",
    });
  }, [subtitle]);

  const handleSave = (field: string) => {
    const updates: Partial<SubtitleItem> = {};
    
    switch (field) {
      case 'startTime':
        updates.startTime = editData.startTime;
        break;
      case 'endTime':
        updates.endTime = editData.endTime;
        break;
      case 'text':
        updates.text = editData.text;
        break;
      case 'translatedText':
        updates.translatedText = editData.translatedText;
        break;
    }
    
    onSave(updates);
    onFieldEdit(null);
  };

  const handleCancel = () => {
    setEditData({
      startTime: subtitle.startTime,
      endTime: subtitle.endTime,
      text: subtitle.text,
      translatedText: subtitle.translatedText || "",
    });
    onFieldEdit(null);
  };

  const handleKeyDown = (e: React.KeyboardEvent, field: string) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave(field);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleSeekToStart = () => {
    const startSeconds = timeStringToSeconds(subtitle.startTime);
    onSeekTo(startSeconds);
  };

  return (
    <div className={cn(
      "grid grid-cols-6 gap-2 p-3 border-b hover:bg-muted/30 transition-colors cursor-pointer",
      editingField && "bg-primary/5 border-primary/20",
      isActive && "bg-accent/50 border-accent"
    )} onClick={handleSeekToStart}>
      {/* Start Time */}
      <div className="flex flex-col gap-1">
        {editingField === 'startTime' ? (
          <Input
            value={editData.startTime}
            onChange={(e) => setEditData(prev => ({ ...prev, startTime: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'startTime')}
            onBlur={() => handleSave('startTime')}
            className="h-8 text-xs"
            placeholder="00:00:00,000"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-xs font-mono bg-muted/50 px-2 py-1 rounded cursor-pointer hover:bg-muted transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('startTime');
            }}
          >
            {subtitle.startTime}
          </div>
        )}
      </div>

      {/* End Time */}
      <div className="flex flex-col gap-1">
        {editingField === 'endTime' ? (
          <Input
            value={editData.endTime}
            onChange={(e) => setEditData(prev => ({ ...prev, endTime: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'endTime')}
            onBlur={() => handleSave('endTime')}
            className="h-8 text-xs"
            placeholder="00:00:03,000"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-xs font-mono bg-muted/50 px-2 py-1 rounded cursor-pointer hover:bg-muted transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('endTime');
            }}
          >
            {subtitle.endTime}
          </div>
        )}
      </div>

      {/* Original Text */}
      <div className="flex flex-col gap-1">
        {editingField === 'text' ? (
          <Textarea
            value={editData.text}
            onChange={(e) => setEditData(prev => ({ ...prev, text: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'text')}
            onBlur={() => handleSave('text')}
            className="min-h-[60px] text-sm resize-none"
            placeholder="Enter subtitle text..."
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-sm leading-relaxed p-2 min-h-[60px] bg-background border rounded cursor-pointer hover:bg-muted/20 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('text');
            }}
          >
            {subtitle.text || (
              <span className="text-muted-foreground italic">Click to add text...</span>
            )}
          </div>
        )}
      </div>

      {/* Translated Text */}
      <div className="flex flex-col gap-1">
        {editingField === 'translatedText' ? (
          <Textarea
            value={editData.translatedText}
            onChange={(e) => setEditData(prev => ({ ...prev, translatedText: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'translatedText')}
            onBlur={() => handleSave('translatedText')}
            className="min-h-[60px] text-sm resize-none"
            placeholder="Enter translation..."
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-sm leading-relaxed p-2 min-h-[60px] bg-background border rounded cursor-pointer hover:bg-muted/20 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('translatedText');
            }}
          >
            {subtitle.translatedText || (
              <span className="text-muted-foreground italic">Click to add translation...</span>
            )}
          </div>
        )}
      </div>

      {/* Duration */}
      <div className="flex flex-col gap-1 justify-center">
        <div className="text-xs text-muted-foreground text-center">
          {((timeStringToSeconds(subtitle.endTime) - timeStringToSeconds(subtitle.startTime))).toFixed(1)}s
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-start gap-1">
        <div className="flex flex-col gap-1">
          <Button 
            size="sm" 
            variant="ghost" 
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }} 
            className="h-7"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          {editingField && (
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={(e) => {
                e.stopPropagation();
                handleCancel();
              }} 
              className="h-7 text-xs"
            >
              ESC
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export const SubtitleEditorWithVideoStack = ({ stackRef }: SubtitleEditorWithVideoStackProps) => {
  const t = useTranslations();
  const subtitleData = useAtomValue(subtitleDataAtom);
  const currentTask = useAtomValue(currentTaskAtom);
  const addSubtitle = useSetAtom(addSubtitleAtom);
  const updateBothSubtitles = useSetAtom(updateBothSubtitlesAtom);
  const deleteSubtitle = useSetAtom(deleteSubtitleAtom);
  const setSubtitleData = useSetAtom(subtitleDataAtom);

  // State for tracking which field is being edited
  const [editingField, setEditingField] = useState<{id: string, field: string} | null>(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const artPlayerRef = useRef<Artplayer | null>(null);
  const [videoError, setVideoError] = useState<string | null>(null);
  const [useHtmlVideo, setUseHtmlVideo] = useState(false);

  // AI prompt state
  const [showPromptInput, setShowPromptInput] = useState(false);
  const [promptText, setPromptText] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);

  // Video info for online videos
  const { data: videoInfo, trigger: getVideoInfo } = useVideoInfo();

  // Fetch video info for online videos when needed
  useEffect(() => {
    if (currentTask.videoUrl && !currentTask.thumbnail) {
      getVideoInfo({ videoUrl: currentTask.videoUrl });
    }
  }, [currentTask.videoUrl, currentTask.thumbnail, getVideoInfo]);

  // Memoize video URL generation to prevent repeated requests
  const videoUrlData = useMemo(() => {
    // Determine the correct video URL to use
    const getVideoUrl = () => {
      // If video has been downloaded, use the downloaded URL
      if (currentTask.DownloadTask?.videoUrl && currentTask.DownloadTask?.downloadStatus === 'success') {
        return currentTask.DownloadTask.videoUrl;
      }
      // Otherwise use the original URL
      return currentTask.videoUrl;
    };

    const videoUrl = getVideoUrl();
    const isUploadedVideo = !!(
      videoUrl &&
      currentTask.thumbnail &&
      currentTask.name &&
      currentTask.duration
    );

    // Smart proxy logic - only proxy external URLs, not data URLs or local files
    const needsVideoProxy = videoUrl?.startsWith('http') && !videoUrl?.includes(window.location.hostname);
    const needsPosterProxy = currentTask.thumbnail?.startsWith('http') && !currentTask.thumbnail?.includes(window.location.hostname) && !currentTask.thumbnail?.startsWith('data:');

    const finalVideoUrl = needsVideoProxy ? `/api/302/vt/video/proxy?url=${encodeURIComponent(videoUrl!)}` : videoUrl;
    const finalPosterUrl = needsPosterProxy ? `/api/302/vt/image/proxy?url=${encodeURIComponent(currentTask.thumbnail!)}` : currentTask.thumbnail;

    return {
      originalUrl: currentTask.videoUrl,
      downloadedUrl: currentTask.DownloadTask?.videoUrl,
      downloadStatus: currentTask.DownloadTask?.downloadStatus,
      videoUrl,
      isUploadedVideo,
      needsVideoProxy,
      needsPosterProxy,
      finalVideoUrl,
      finalPosterUrl
    };
  }, [
    currentTask.videoUrl,
    currentTask.DownloadTask?.videoUrl,
    currentTask.DownloadTask?.downloadStatus,
    currentTask.thumbnail,
    currentTask.name,
    currentTask.duration
  ]);

  const handleBack = () => {
    stackRef.current?.pop();
  };

  const handleAddSubtitle = () => {
    const lastSubtitle = subtitleData.originalSubtitles[subtitleData.originalSubtitles.length - 1];
    const startTime = lastSubtitle ? lastSubtitle.endTime : secondsToTimeString(currentTime);

    addSubtitle({
      startTime,
      endTime: secondsToTimeString(currentTime + 3),
      text: "New subtitle text",
    });

    toast.success("New subtitle added");
  };

  const handleFieldEdit = (id: string, field: string | null) => {
    if (field) {
      setEditingField({ id, field });
    } else {
      setEditingField(null);
    }
  };

  const handleSaveSubtitle = (id: string, updates: Partial<SubtitleItem>) => {
    updateBothSubtitles({ id, updates });
    setEditingField(null);
    toast.success("Subtitle updated");
  };

  const handleDeleteSubtitle = (id: string) => {
    deleteSubtitle(id);
    setEditingField(null);
    toast.success("Subtitle deleted");
  };

  const handleGenerateSubtitles = async () => {
    if (!promptText.trim()) {
      toast.error("Please enter a prompt");
      return;
    }

    if (!currentTask.videoUrl) {
      toast.error("Please select a video first");
      return;
    }

    setIsGenerating(true);

    try {
      // Get language names for the prompt
      const sourceLanguage = currentTask.settings?.sourceLanguage || 'en';
      const targetLanguage = currentTask.settings?.targetLanguage || 'zh';

      // Map language codes to names
      const getLanguageName = (code: string) => {
        const languageMap: Record<string, string> = {
          'en': 'English',
          'zh': 'Chinese',
          'es': 'Spanish',
          'fr': 'French',
          'de': 'German',
          'ja': 'Japanese',
          'ko': 'Korean',
          'pt': 'Portuguese',
          'ru': 'Russian',
          'ar': 'Arabic',
          'hi': 'Hindi',
          'it': 'Italian',
          'nl': 'Dutch',
          'sv': 'Swedish',
          'da': 'Danish',
          'no': 'Norwegian',
          'fi': 'Finnish',
          'pl': 'Polish',
          'tr': 'Turkish',
          'th': 'Thai',
          'vi': 'Vietnamese',
          'id': 'Indonesian',
          'ms': 'Malay',
          'tl': 'Filipino',
          'ur': 'Urdu',
          'bn': 'Bengali',
          'ta': 'Tamil',
          'te': 'Telugu',
          'ml': 'Malayalam',
          'kn': 'Kannada',
          'gu': 'Gujarati',
          'pa': 'Punjabi',
          'or': 'Odia',
          'as': 'Assamese',
          'ne': 'Nepali',
          'si': 'Sinhala',
          'my': 'Myanmar',
          'km': 'Khmer',
          'lo': 'Lao',
          'ka': 'Georgian',
          'am': 'Amharic',
          'sw': 'Swahili',
          'zu': 'Zulu',
          'af': 'Afrikaans',
          'sq': 'Albanian',
          'eu': 'Basque',
          'be': 'Belarusian',
          'bg': 'Bulgarian',
          'ca': 'Catalan',
          'hr': 'Croatian',
          'cs': 'Czech',
          'et': 'Estonian',
          'gl': 'Galician',
          'el': 'Greek',
          'he': 'Hebrew',
          'hu': 'Hungarian',
          'is': 'Icelandic',
          'ga': 'Irish',
          'lv': 'Latvian',
          'lt': 'Lithuanian',
          'mk': 'Macedonian',
          'mt': 'Maltese',
          'ro': 'Romanian',
          'sk': 'Slovak',
          'sl': 'Slovenian',
          'sr': 'Serbian',
          'uk': 'Ukrainian',
          'cy': 'Welsh'
        };
        return languageMap[code] || code.toUpperCase();
      };

      const originalLanguageName = getLanguageName(sourceLanguage);
      const targetLanguageName = getLanguageName(targetLanguage);

      // Prepare the AI prompt for subtitle generation
      const aiPrompt = `Generate subtitles for a video based on this prompt: "${promptText}"

Video details:
- Duration: ${currentTask.duration || videoInfo?.info?.duration || 'unknown'} seconds
- Source language: ${originalLanguageName} (${sourceLanguage})
- Target language: ${targetLanguageName} (${targetLanguage})

Please generate 5-10 subtitle segments in SRT format with both original and translated text. Use this exact format:

1
00:00:01,234 --> 00:00:04,567
This is the original sentence in ${originalLanguageName}.
This is the translated sentence in ${targetLanguageName}.

2
00:00:04,567 --> 00:00:07,890
Another original sentence in ${originalLanguageName}.
Another translated sentence in ${targetLanguageName}.

Make sure:
- Each subtitle has a sequential number
- Timing is realistic and matches the video duration
- Each subtitle block has exactly 2 lines: original text first, then translated text
- Content matches the user's prompt
- Return only the SRT content, no additional text or explanations.`;

      // Call 302.ai API
      const response = await fetch('https://api.302.ai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer sk-gJj2ExFbK8qdFbVgfRQY9FfmpYkte8PGCkI4LCVoBnitvkgx',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: "gemini-1.5-pro",
          messages: [
            {
              role: "user",
              content: aiPrompt
            }
          ]
        }),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const result = await response.json();

      if (result.choices && result.choices[0] && result.choices[0].message) {
        const aiResponse = result.choices[0].message.content;

        try {
          // Parse SRT format response
          const parsedSubtitles = parseSRTContent(aiResponse);

          if (parsedSubtitles.length > 0) {
            setSubtitleData({
              prompt: promptText,
              originalSubtitles: parsedSubtitles,
              translatedSubtitles: parsedSubtitles.map((sub: SubtitleItem) => ({ ...sub, translatedText: sub.translatedText || "" })),
            });

            toast.success("Subtitles generated successfully!");
            setShowPromptInput(false);
            setPromptText("");
            return;
          } else {
            throw new Error("No valid subtitles found in AI response");
          }
        } catch (parseError) {
          console.error("Failed to parse SRT response:", parseError);
          throw new Error("AI response was not in valid SRT format");
        }
      } else {
        throw new Error("Invalid response from AI API");
      }

    } catch (error) {
      console.error("Error generating subtitles:", error);

      // Fallback to mock data if API fails
      const sourceLanguage = currentTask.settings?.sourceLanguage || 'en';
      const targetLanguage = currentTask.settings?.targetLanguage || 'zh';

      const mockSubtitles = [
        {
          id: "1",
          startTime: "00:00:00,000",
          endTime: "00:00:03,000",
          text: "Welcome to our tutorial",
          translatedText: sourceLanguage !== targetLanguage ? "欢迎来到我们的教程" : "",
        },
        {
          id: "2",
          startTime: "00:00:03,000",
          endTime: "00:00:06,000",
          text: "Today we'll learn something new",
          translatedText: sourceLanguage !== targetLanguage ? "今天我们将学习新的内容" : "",
        },
        {
          id: "3",
          startTime: "00:00:06,000",
          endTime: "00:00:09,000",
          text: "Let's get started with the basics",
          translatedText: sourceLanguage !== targetLanguage ? "让我们从基础开始" : "",
        },
      ];

      setSubtitleData({
        prompt: promptText,
        originalSubtitles: mockSubtitles,
        translatedSubtitles: mockSubtitles.map((sub: SubtitleItem) => ({ ...sub, translatedText: sub.translatedText || "" })),
      });

      toast.success("Subtitles generated (using fallback data)");
      setShowPromptInput(false);
      setPromptText("");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSeekTo = (time: number) => {
    if (artPlayerRef.current) {
      artPlayerRef.current.seek = time;
      setCurrentTime(time);
      toast.success(`Seeking to ${time.toFixed(1)}s`);
    } else {
      toast.error("Video player not ready");
    }
  };

  const handleExportSubtitles = () => {
    const srtContent = subtitleData.originalSubtitles
      .map((sub, index) => {
        return `${index + 1}\n${sub.startTime.replace(',', ' --> ')}${sub.endTime}\n${sub.text}\n`;
      })
      .join('\n');

    const blob = new Blob([srtContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'subtitles.srt';
    a.click();
    URL.revokeObjectURL(url);

    toast.success("Subtitles exported");
  };

  // Get current active subtitle based on video time
  const getCurrentSubtitle = () => {
    return subtitleData.originalSubtitles.find(subtitle => {
      const startTime = timeStringToSeconds(subtitle.startTime);
      const endTime = timeStringToSeconds(subtitle.endTime);
      return currentTime >= startTime && currentTime <= endTime;
    });
  };

  const currentSubtitle = getCurrentSubtitle();

  // Video player setup with real ArtPlayer
  const handlePlayerInstance = (art: Artplayer) => {
    console.log('ArtPlayer instance created:', art);
    artPlayerRef.current = art;

    // Real-time subtitle synchronization
    art.on('video:timeupdate', () => {
      const currentVideoTime = art.currentTime;
      setCurrentTime(currentVideoTime);

      // Update subtitle overlay based on current time
      updateSubtitleOverlay(currentVideoTime);
    });

    art.on('video:play', () => {
      console.log('Video started playing');
      setIsPlaying(true);
    });

    art.on('video:pause', () => {
      console.log('Video paused');
      setIsPlaying(false);
    });

    art.on('ready', () => {
      console.log('Video player ready, URL:', art.url);
      // Initialize subtitle overlay
      updateSubtitleOverlay(0);
    });

    art.on('error', (error) => {
      console.error('Video player error:', error);
      setVideoError(error.message || 'Video failed to load');
      setUseHtmlVideo(true);
      toast.error(`Video error: ${error.message || 'Unknown error'}`);
    });
  };

  // Function to update subtitle overlay based on current time
  const updateSubtitleOverlay = (currentVideoTime: number) => {
    // Find the current subtitle based on video time
    const currentSub = subtitleData.originalSubtitles.find(sub => {
      const startTime = timeStringToSeconds(sub.startTime);
      const endTime = timeStringToSeconds(sub.endTime);
      return currentVideoTime >= startTime && currentVideoTime <= endTime;
    });

    // For ArtPlayer
    if (artPlayerRef.current && !useHtmlVideo) {
      // Update subtitle display on video
      if (currentSub) {
        const subtitleHtml = `
          <div style="position: absolute; bottom: 60px; left: 50%; transform: translateX(-50%); text-align: center; z-index: 100; pointer-events: none;">
            ${currentSub.translatedText ? `<div style="background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; margin: 2px; border-radius: 4px; font-size: 14px;">${currentSub.translatedText}</div>` : ''}
            <div style="background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; margin: 2px; border-radius: 4px; font-size: 16px;">${currentSub.text}</div>
          </div>
        `;

        // Remove existing subtitle overlay
        const existingOverlay = artPlayerRef.current.template.$player.querySelector('.custom-subtitle-overlay');
        if (existingOverlay) {
          existingOverlay.remove();
        }

        // Add new subtitle overlay
        const overlayDiv = document.createElement('div');
        overlayDiv.className = 'custom-subtitle-overlay';
        overlayDiv.innerHTML = subtitleHtml;
        artPlayerRef.current.template.$player.appendChild(overlayDiv);
      } else {
        // Remove subtitle overlay if no current subtitle
        const existingOverlay = artPlayerRef.current.template.$player.querySelector('.custom-subtitle-overlay');
        if (existingOverlay) {
          existingOverlay.remove();
        }
      }
    }

    // For HTML5 video (fallback)
    if (useHtmlVideo || videoError) {
      const videoContainer = document.querySelector('.aspect-video');
      if (!videoContainer) return;

      // Remove existing subtitle overlay
      const existingOverlay = videoContainer.querySelector('.html5-subtitle-overlay');
      if (existingOverlay) {
        existingOverlay.remove();
      }

      // Add new subtitle overlay if there's a current subtitle
      if (currentSub) {
        const overlayDiv = document.createElement('div');
        overlayDiv.className = 'html5-subtitle-overlay';
        overlayDiv.style.cssText = `
          position: absolute;
          bottom: 60px;
          left: 50%;
          transform: translateX(-50%);
          text-align: center;
          z-index: 100;
          pointer-events: none;
        `;

        const subtitleHtml = `
          ${currentSub.translatedText ? `<div style="background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; margin: 2px; border-radius: 4px; font-size: 14px;">${currentSub.translatedText}</div>` : ''}
          <div style="background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; margin: 2px; border-radius: 4px; font-size: 16px;">${currentSub.text}</div>
        `;

        overlayDiv.innerHTML = subtitleHtml;
        videoContainer.appendChild(overlayDiv);
      }
    }
  };

  // Update subtitle overlay when subtitle data changes
  useEffect(() => {
    if (artPlayerRef.current) {
      updateSubtitleOverlay(currentTime);
    }
  }, [subtitleData.originalSubtitles, currentTime]);

  return (
    <>
      {/* Header */}
      <div className="flex items-center gap-3 border-b bg-card/50 p-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="h-7 w-7 p-0"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex items-center gap-2 flex-1">
          <Edit3 className="h-4 w-4 text-primary" />
          <h2 className="text-sm font-medium">
            {t("form.fields.subtitleEditor.title")} with Video Preview
          </h2>
        </div>
      </div>

      {/* Main Content - Split Layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Video Preview Panel */}
        <div className="w-1/2 border-r flex flex-col">
          {/* Video Player - Sticky */}
          <div className="sticky top-0 z-10 bg-background shadow-sm">
            <div className="p-4">
              <div className="aspect-video bg-black rounded-lg overflow-hidden">
              {(() => {
                // Use memoized video URL data to prevent repeated requests
                console.log('Video URL determination:', {
                  originalUrl: videoUrlData.originalUrl,
                  downloadedUrl: videoUrlData.downloadedUrl,
                  downloadStatus: videoUrlData.downloadStatus,
                  finalVideoUrl: videoUrlData.videoUrl,
                  isUploadedVideo: videoUrlData.isUploadedVideo
                });

                if (videoUrlData.isUploadedVideo) {
                  console.log('Loading video with smart proxy:', {
                    originalUrl: videoUrlData.videoUrl,
                    needsVideoProxy: videoUrlData.needsVideoProxy,
                    finalVideoUrl: videoUrlData.finalVideoUrl,
                    originalPoster: currentTask.thumbnail,
                    needsPosterProxy: videoUrlData.needsPosterProxy,
                    finalPosterUrl: videoUrlData.finalPosterUrl,
                    name: currentTask.name,
                    duration: currentTask.duration,
                    isDownloaded: !!currentTask.DownloadTask?.videoUrl
                  });

                  if (useHtmlVideo || videoError) {
                    return (
                      <div className="relative w-full h-full">
                        <video
                          src={videoUrlData.finalVideoUrl}
                          poster={videoUrlData.finalPosterUrl}
                          controls
                          className="w-full h-full object-contain"
                          onTimeUpdate={(e) => {
                            const video = e.target as HTMLVideoElement;
                            setCurrentTime(video.currentTime);
                            updateSubtitleOverlay(video.currentTime);
                          }}
                          onPlay={() => setIsPlaying(true)}
                          onPause={() => setIsPlaying(false)}
                          onError={(e) => {
                            console.error('HTML5 video error:', e);
                            toast.error('Video failed to load');
                          }}
                        />
                        {videoError && (
                          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs">
                            Fallback player (ArtPlayer failed)
                          </div>
                        )}
                        {currentTask.DownloadTask?.videoUrl && (
                          <div className="absolute top-2 right-2 bg-green-600 text-white px-2 py-1 rounded text-xs">
                            Downloaded Video
                          </div>
                        )}
                        {(videoUrlData.needsVideoProxy || videoUrlData.needsPosterProxy) && (
                          <div className="absolute bottom-2 left-2 bg-blue-600 text-white px-2 py-1 rounded text-xs">
                            Proxied
                          </div>
                        )}
                      </div>
                    );
                  }

                  return (
                    <ArtPlayer
                      url={videoUrlData.finalVideoUrl || ''}
                      poster={videoUrlData.finalPosterUrl || ''}
                      className="w-full h-full"
                      getInstance={handlePlayerInstance}
                    />
                  );
                } else if (videoInfo?.info) {
                  const videoUrl = currentTask.videoUrl && isYoutubeUrl(currentTask.videoUrl)
                    ? currentTask.videoUrl
                    : currentTask.settings?.selectedFormat?.url || "";

                  console.log('Loading online video:', {
                    originalUrl: currentTask.videoUrl,
                    selectedFormatUrl: currentTask.settings?.selectedFormat?.url,
                    finalVideoUrl: videoUrl,
                    isYoutube: isYoutubeUrl(videoUrl),
                    videoInfo: videoInfo.info
                  });

                  if (isYoutubeUrl(videoUrl)) {
                    const youtubeVideoId = getYoutubeVideoId(videoUrl);
                    console.log('Loading YouTube video:', youtubeVideoId);
                    return (
                      <iframe
                        src={`https://www.youtube.com/embed/${youtubeVideoId}`}
                        className="w-full h-full"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                      />
                    );
                  } else {
                    const proxyUrl = `/api/302/vt/video/proxy?url=${encodeURIComponent(videoUrl)}`;
                    const posterUrl = `/api/302/vt/image/proxy?url=${encodeURIComponent(videoInfo.info.thumbnail)}`;
                    console.log('Loading proxied video:', { proxyUrl, posterUrl });

                    if (useHtmlVideo || videoError) {
                      return (
                        <div className="relative w-full h-full">
                          <video
                            src={proxyUrl}
                            poster={posterUrl}
                            controls
                            className="w-full h-full object-contain"
                            onTimeUpdate={(e) => {
                              const video = e.target as HTMLVideoElement;
                              setCurrentTime(video.currentTime);
                              updateSubtitleOverlay(video.currentTime);
                            }}
                            onPlay={() => setIsPlaying(true)}
                            onPause={() => setIsPlaying(false)}
                            onError={(e) => {
                              console.error('HTML5 video error:', e);
                              toast.error('Video failed to load');
                            }}
                          />
                          {videoError && (
                            <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs">
                              Fallback player (ArtPlayer failed)
                            </div>
                          )}
                        </div>
                      );
                    }

                    return (
                      <ArtPlayer
                        url={proxyUrl}
                        poster={posterUrl}
                        className="w-full h-full"
                        getInstance={handlePlayerInstance}
                      />
                    );
                  }
                } else if (currentTask.videoUrl) {
                  console.log('Video URL exists but no video info:', currentTask.videoUrl);
                  return (
                    <div className="w-full h-full flex items-center justify-center text-white">
                      <div className="text-center space-y-2">
                        <div className="text-sm font-medium">Loading video...</div>
                        <div className="text-xs text-muted-foreground">Please wait</div>
                        <div className="text-xs text-gray-400 mt-2">URL: {currentTask.videoUrl.substring(0, 50)}...</div>
                      </div>
                    </div>
                  );
                } else {
                  console.log('No video URL available');
                  return (
                    <div className="w-full h-full flex items-center justify-center text-white">
                      <div className="text-center space-y-2">
                        <div className="text-sm font-medium">No video loaded</div>
                        <div className="text-xs text-muted-foreground">Please select a video from upload or online video first</div>
                      </div>
                    </div>
                  );
                }
              })()}
              </div>
            </div>
          </div>

          {/* Video Controls - Also Sticky */}
          <div className="sticky top-[400px] z-10 bg-background border-t shadow-sm">
            <div className="p-3 bg-muted/30">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-4">
                <span>Time: {currentTime.toFixed(1)}s</span>
                {currentSubtitle && (
                  <Badge variant="secondary">
                    Active: {timeStringToSeconds(currentSubtitle.startTime).toFixed(1)}s - {timeStringToSeconds(currentSubtitle.endTime).toFixed(1)}s
                  </Badge>
                )}
                <span className={`text-xs ${isPlaying ? 'text-green-600' : 'text-gray-500'}`}>
                  {isPlaying ? '▶ Playing' : '⏸ Paused'}
                </span>
                {currentTask.DownloadTask?.videoUrl && currentTask.DownloadTask?.downloadStatus === 'success' && (
                  <Badge variant="default" className="bg-green-600">
                    Downloaded
                  </Badge>
                )}
                {currentTask.videoUrl && !currentTask.DownloadTask?.videoUrl && (
                  <Badge variant="outline">
                    Original URL
                  </Badge>
                )}
                {!useHtmlVideo && !videoError && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setUseHtmlVideo(true);
                      toast.info('Switched to HTML5 video player');
                    }}
                  >
                    Try HTML5 Player
                  </Button>
                )}
                {(useHtmlVideo || videoError) && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setUseHtmlVideo(false);
                      setVideoError(null);
                      toast.info('Switched back to ArtPlayer');
                    }}
                  >
                    Try ArtPlayer
                  </Button>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => {
                    const debugInfo = {
                      ...videoUrlData,
                      thumbnail: currentTask.thumbnail,
                      thumbnailType: currentTask.thumbnail?.startsWith('data:') ? 'base64' : currentTask.thumbnail?.startsWith('http') ? 'external' : 'local',
                      name: currentTask.name,
                      duration: currentTask.duration,
                      useHtmlVideo,
                      videoError
                    };
                    console.log('Video Debug Info:', debugInfo);
                    toast.info('Debug info logged to console');
                  }}
                >
                  Debug
                </Button>
                <Button size="sm" onClick={handleAddSubtitle}>
                  <Plus className="h-3 w-3 mr-1" />
                  Add at {currentTime.toFixed(1)}s
                </Button>
              </div>
            </div>
            </div>
          </div>

          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto">
            {/* This area will scroll while video stays sticky */}
          </div>
        </div>

        {/* Subtitle Editor Panel */}
        <div className="w-1/2 flex flex-col overflow-hidden">
          {/* Action Bar - Sticky */}
          <div className="sticky top-0 z-10 bg-background border-b shadow-sm">
            <div className="flex items-center gap-2 bg-muted/30 p-3">
            <Button
              size="sm"
              variant="default"
              onClick={() => setShowPromptInput(!showPromptInput)}
              disabled={isGenerating}
            >
              <Wand2 className="h-3 w-3 mr-1" />
              Create with AI
            </Button>
            <Button size="sm" onClick={handleAddSubtitle}>
              <Plus className="h-3 w-3 mr-1" />
              {t("form.fields.subtitleEditor.addSubtitle")}
            </Button>
            <Button size="sm" variant="outline" onClick={handleExportSubtitles}>
              <Download className="h-3 w-3 mr-1" />
              {t("form.fields.subtitleEditor.exportSubtitles")}
            </Button>
            <div className="ml-auto text-xs text-muted-foreground">
              {subtitleData.originalSubtitles.length} subtitles
            </div>
            </div>
          </div>

          {/* AI Prompt Input */}
          {showPromptInput && (
            <div className="border-b bg-card p-4 space-y-3">
              <div className="space-y-2">
                <label className="text-sm font-medium">AI Prompt</label>
                <Textarea
                  value={promptText}
                  onChange={(e) => setPromptText(e.target.value)}
                  placeholder="Enter a prompt to generate subtitles (e.g., 'Create subtitles for a cooking tutorial about making pasta')"
                  className="min-h-[80px] resize-none"
                  disabled={isGenerating}
                />
              </div>
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  onClick={handleGenerateSubtitles}
                  disabled={isGenerating || !promptText.trim()}
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-3 w-3 mr-1" />
                      Generate Subtitles
                    </>
                  )}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setShowPromptInput(false);
                    setPromptText("");
                  }}
                  disabled={isGenerating}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}

          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto">
            {subtitleData.originalSubtitles.length === 0 ? (
              <div className="h-full flex items-center justify-center">
                <div className="text-center space-y-3">
                  <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                    <Edit3 className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <div>
                    <h3 className="font-medium">{t("form.fields.subtitleEditor.noSubtitles")}</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Click "Add Subtitle" to create your first subtitle.
                    </p>
                  </div>
                  <Button onClick={handleAddSubtitle}>
                    <Plus className="h-4 w-4 mr-2" />
                    {t("form.fields.subtitleEditor.addSubtitle")}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col">
                {/* Table Header - Sticky within scroll area */}
                <div className="sticky top-0 z-5 bg-background border-b shadow-sm">
                  <div className="grid grid-cols-6 gap-2 p-3 bg-muted/50 font-medium text-sm">
                    <div>{t("form.fields.subtitleEditor.timeStart")}</div>
                    <div>{t("form.fields.subtitleEditor.timeEnd")}</div>
                    <div>{t("form.fields.subtitleEditor.originalSubtitles")}</div>
                    <div>{t("form.fields.subtitleEditor.translatedSubtitles")}</div>
                    <div>Duration</div>
                    <div>Actions</div>
                  </div>
                </div>

                {/* Table Content */}
                <div>
                  {subtitleData.originalSubtitles.map((subtitle) => {
                    // Find the corresponding translated subtitle
                    const translatedSubtitle = subtitleData.translatedSubtitles.find(t => t.id === subtitle.id);
                    const combinedSubtitle = {
                      ...subtitle,
                      translatedText: translatedSubtitle?.translatedText || ""
                    };

                    return (
                      <SubtitleRow
                        key={subtitle.id}
                        subtitle={combinedSubtitle}
                        editingField={editingField?.id === subtitle.id ? editingField.field : null}
                        onFieldEdit={(field) => handleFieldEdit(subtitle.id, field)}
                        onSave={(updates) => handleSaveSubtitle(subtitle.id, updates)}
                        onDelete={() => handleDeleteSubtitle(subtitle.id)}
                        isActive={currentSubtitle?.id === subtitle.id}
                        onSeekTo={handleSeekTo}
                      />
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
