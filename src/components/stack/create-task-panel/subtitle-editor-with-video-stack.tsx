"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { StackRef } from "@/components/ui/stack";
import { ArrowLeft, Edit3, Plus, Download, Trash2, Play, Pause, Wand2, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { RefObject, useState, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAtomValue, useSetAtom } from "jotai";
import {
  subtitleDataAtom,
  addSubtitleAtom,
  updateBothSubtitlesAtom,
  deleteSubtitleAtom,
  SubtitleItem
} from "@/stores/slices/subtitle_store";
import { currentTaskAtom } from "@/stores/slices/current_task";
import { VideoPreview } from "@/components/common/video/video-preview";
import { useVideoInfo } from "@/hooks/swr/use-video-info";
import { isYoutubeUrl } from "@/utils/video-format";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { assColorToCss } from "@/utils/color";
import { SubtitleStyle, SubtitleLayout } from "@/stores/slices/current_task";

// Custom subtitle overlay component for actual subtitle content
const CustomSubtitleOverlay: React.FC<{
  subtitle: SubtitleItem;
  style?: SubtitleStyle;
  layout?: SubtitleLayout;
}> = ({ subtitle, style, layout = "double" }) => {
  if (!style) {
    // Simple fallback styling
    return (
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute bottom-4 left-4 right-4">
          <div className="text-center space-y-1">
            {/* Translated Text */}
            {subtitle.translatedText && (
              <div className="bg-black/70 text-white px-3 py-1 rounded text-sm">
                {subtitle.translatedText}
              </div>
            )}
            {/* Original Text */}
            <div className="bg-black/70 text-white px-3 py-1 rounded text-sm">
              {subtitle.text}
            </div>
          </div>
        </div>
      </div>
    );
  }

  const {
    fontSize,
    fontFamily,
    primaryColor,
    primaryStrokeWidth,
    shadowColor,
    secondaryFontSize,
    secondaryFontFamily,
    secondaryColor,
    secondaryStrokeColor,
    secondaryStrokeWidth,
    secondaryBackgroundColor,
    primaryMarginV,
    secondaryMarginV,
  } = style;

  const commonStyle: React.CSSProperties = {
    position: "absolute",
    left: "50%",
    transform: "translateX(-50%)",
    textAlign: "center",
    maxWidth: "90%",
    width: "fit-content",
    padding: "4px",
    pointerEvents: "none",
    zIndex: 99,
  };

  const primaryStyle: React.CSSProperties = {
    ...commonStyle,
    fontFamily,
    fontSize: `${fontSize}px`,
    color: assColorToCss(primaryColor),
    WebkitTextStroke: style.showPrimaryStroke
      ? `${primaryStrokeWidth}px ${assColorToCss(shadowColor)}`
      : "none",
    textShadow: style.showPrimaryShadow
      ? `2px 2px 2px ${assColorToCss(shadowColor)}`
      : "none",
  };

  const secondaryStyle: React.CSSProperties = {
    ...commonStyle,
    fontFamily: secondaryFontFamily,
    fontSize: `${secondaryFontSize}px`,
    color: assColorToCss(secondaryColor),
    WebkitTextStroke: style.showSecondaryStroke
      ? `${secondaryStrokeWidth}px ${assColorToCss(secondaryStrokeColor)}`
      : "none",
    textShadow: style.showSecondaryShadow
      ? `2px 2px 2px ${assColorToCss(secondaryStrokeColor)}`
      : "none",
    backgroundColor: style.showSecondaryBackground
      ? assColorToCss(secondaryBackgroundColor)
      : "transparent",
  };

  if (layout === "single") {
    return (
      <div className="absolute inset-0 pointer-events-none">
        <div
          style={{
            ...secondaryStyle,
            bottom: `${secondaryMarginV}px`,
            position: "absolute",
            width: "100%",
          }}
        >
          {subtitle.text}
        </div>
      </div>
    );
  }

  return (
    <div className="absolute inset-0 pointer-events-none">
      {/* Translated Text */}
      {subtitle.translatedText && (
        <div
          style={{
            ...secondaryStyle,
            bottom: `${secondaryMarginV}px`,
            position: "absolute",
            width: "100%",
          }}
        >
          {subtitle.translatedText}
        </div>
      )}
      {/* Original Text */}
      <div
        style={{
          ...primaryStyle,
          bottom: `${primaryMarginV}px`,
          position: "absolute",
          width: "100%",
        }}
      >
        {subtitle.text}
      </div>
    </div>
  );
};

// Helper function to parse SRT content
function parseSRTContent(srtContent: string): SubtitleItem[] {
  const subtitles: SubtitleItem[] = [];

  // Split by double newlines to separate subtitle blocks
  const blocks = srtContent.trim().split(/\n\s*\n/);

  for (const block of blocks) {
    const lines = block.trim().split('\n');

    if (lines.length >= 3) {
      // First line should be the subtitle number
      const numberMatch = lines[0].match(/^\d+$/);
      if (!numberMatch) continue;

      // Second line should be the timing
      const timingMatch = lines[1].match(/^(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})$/);
      if (!timingMatch) continue;

      const startTime = timingMatch[1];
      const endTime = timingMatch[2];

      // Remaining lines are the subtitle text
      const textLines = lines.slice(2);

      // First line is original text, second line is translated text (if present)
      const originalText = textLines[0] || '';
      const translatedText = textLines[1] || '';

      subtitles.push({
        id: numberMatch[0],
        startTime,
        endTime,
        text: originalText,
        translatedText: translatedText
      });
    }
  }

  return subtitles;
}

interface SubtitleEditorWithVideoStackProps {
  stackRef: RefObject<StackRef>;
}

interface SubtitleRowProps {
  subtitle: SubtitleItem;
  editingField: string | null;
  onFieldEdit: (field: string | null) => void;
  onSave: (updates: Partial<SubtitleItem>) => void;
  onDelete: () => void;
  isActive: boolean;
  onSeekTo: (time: number) => void;
}

// Convert time string to seconds
const timeStringToSeconds = (timeString: string): number => {
  const [time, milliseconds] = timeString.split(',');
  const [hours, minutes, seconds] = time.split(':').map(Number);
  return hours * 3600 + minutes * 60 + seconds + (parseInt(milliseconds || '0') / 1000);
};

// Convert seconds to time string
const secondsToTimeString = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const milliseconds = Math.floor((seconds % 1) * 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
};

const SubtitleRow = ({ 
  subtitle, 
  editingField, 
  onFieldEdit, 
  onSave, 
  onDelete,
  isActive,
  onSeekTo
}: SubtitleRowProps) => {
  const [editData, setEditData] = useState({
    startTime: subtitle.startTime,
    endTime: subtitle.endTime,
    text: subtitle.text,
    translatedText: subtitle.translatedText || "",
  });

  // Update edit data when subtitle changes
  useEffect(() => {
    setEditData({
      startTime: subtitle.startTime,
      endTime: subtitle.endTime,
      text: subtitle.text,
      translatedText: subtitle.translatedText || "",
    });
  }, [subtitle]);

  const handleSave = (field: string) => {
    const updates: Partial<SubtitleItem> = {};
    
    switch (field) {
      case 'startTime':
        updates.startTime = editData.startTime;
        break;
      case 'endTime':
        updates.endTime = editData.endTime;
        break;
      case 'text':
        updates.text = editData.text;
        break;
      case 'translatedText':
        updates.translatedText = editData.translatedText;
        break;
    }
    
    onSave(updates);
    onFieldEdit(null);
  };

  const handleCancel = () => {
    setEditData({
      startTime: subtitle.startTime,
      endTime: subtitle.endTime,
      text: subtitle.text,
      translatedText: subtitle.translatedText || "",
    });
    onFieldEdit(null);
  };

  const handleKeyDown = (e: React.KeyboardEvent, field: string) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSave(field);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleSeekToStart = () => {
    const startSeconds = timeStringToSeconds(subtitle.startTime);
    onSeekTo(startSeconds);
  };

  return (
    <div className={cn(
      "grid grid-cols-6 gap-2 p-3 border-b hover:bg-muted/30 transition-colors cursor-pointer",
      editingField && "bg-primary/5 border-primary/20",
      isActive && "bg-accent/50 border-accent"
    )} onClick={handleSeekToStart}>
      {/* Start Time */}
      <div className="flex flex-col gap-1">
        {editingField === 'startTime' ? (
          <Input
            value={editData.startTime}
            onChange={(e) => setEditData(prev => ({ ...prev, startTime: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'startTime')}
            onBlur={() => handleSave('startTime')}
            className="h-8 text-xs"
            placeholder="00:00:00,000"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-xs font-mono bg-muted/50 px-2 py-1 rounded cursor-pointer hover:bg-muted transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('startTime');
            }}
          >
            {subtitle.startTime}
          </div>
        )}
      </div>

      {/* End Time */}
      <div className="flex flex-col gap-1">
        {editingField === 'endTime' ? (
          <Input
            value={editData.endTime}
            onChange={(e) => setEditData(prev => ({ ...prev, endTime: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'endTime')}
            onBlur={() => handleSave('endTime')}
            className="h-8 text-xs"
            placeholder="00:00:03,000"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-xs font-mono bg-muted/50 px-2 py-1 rounded cursor-pointer hover:bg-muted transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('endTime');
            }}
          >
            {subtitle.endTime}
          </div>
        )}
      </div>

      {/* Original Text */}
      <div className="flex flex-col gap-1">
        {editingField === 'text' ? (
          <Textarea
            value={editData.text}
            onChange={(e) => setEditData(prev => ({ ...prev, text: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'text')}
            onBlur={() => handleSave('text')}
            className="min-h-[60px] text-sm resize-none"
            placeholder="Enter subtitle text..."
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-sm leading-relaxed p-2 min-h-[60px] bg-background border rounded cursor-pointer hover:bg-muted/20 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('text');
            }}
          >
            {subtitle.text || (
              <span className="text-muted-foreground italic">Click to add text...</span>
            )}
          </div>
        )}
      </div>

      {/* Translated Text */}
      <div className="flex flex-col gap-1">
        {editingField === 'translatedText' ? (
          <Textarea
            value={editData.translatedText}
            onChange={(e) => setEditData(prev => ({ ...prev, translatedText: e.target.value }))}
            onKeyDown={(e) => handleKeyDown(e, 'translatedText')}
            onBlur={() => handleSave('translatedText')}
            className="min-h-[60px] text-sm resize-none"
            placeholder="Enter translation..."
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <div 
            className="text-sm leading-relaxed p-2 min-h-[60px] bg-background border rounded cursor-pointer hover:bg-muted/20 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              onFieldEdit('translatedText');
            }}
          >
            {subtitle.translatedText || (
              <span className="text-muted-foreground italic">Click to add translation...</span>
            )}
          </div>
        )}
      </div>

      {/* Duration */}
      <div className="flex flex-col gap-1 justify-center">
        <div className="text-xs text-muted-foreground text-center">
          {((timeStringToSeconds(subtitle.endTime) - timeStringToSeconds(subtitle.startTime))).toFixed(1)}s
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-start gap-1">
        <div className="flex flex-col gap-1">
          <Button 
            size="sm" 
            variant="ghost" 
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }} 
            className="h-7"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
          {editingField && (
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={(e) => {
                e.stopPropagation();
                handleCancel();
              }} 
              className="h-7 text-xs"
            >
              ESC
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export const SubtitleEditorWithVideoStack = ({ stackRef }: SubtitleEditorWithVideoStackProps) => {
  const t = useTranslations();
  const subtitleData = useAtomValue(subtitleDataAtom);
  const currentTask = useAtomValue(currentTaskAtom);
  const addSubtitle = useSetAtom(addSubtitleAtom);
  const updateBothSubtitles = useSetAtom(updateBothSubtitlesAtom);
  const deleteSubtitle = useSetAtom(deleteSubtitleAtom);
  const setSubtitleData = useSetAtom(subtitleDataAtom);

  // State for tracking which field is being edited
  const [editingField, setEditingField] = useState<{id: string, field: string} | null>(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  // AI prompt state
  const [showPromptInput, setShowPromptInput] = useState(false);
  const [promptText, setPromptText] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);

  // Video info for online videos
  const { data: videoInfo, trigger: getVideoInfo } = useVideoInfo();

  // Fetch video info for online videos when needed
  useEffect(() => {
    if (currentTask.videoUrl && !currentTask.thumbnail) {
      getVideoInfo({ videoUrl: currentTask.videoUrl });
    }
  }, [currentTask.videoUrl, currentTask.thumbnail, getVideoInfo]);

  const handleBack = () => {
    stackRef.current?.pop();
  };

  const handleAddSubtitle = () => {
    const lastSubtitle = subtitleData.originalSubtitles[subtitleData.originalSubtitles.length - 1];
    const startTime = lastSubtitle ? lastSubtitle.endTime : secondsToTimeString(currentTime);

    addSubtitle({
      startTime,
      endTime: secondsToTimeString(currentTime + 3),
      text: "New subtitle text",
    });

    toast.success("New subtitle added");
  };

  const handleFieldEdit = (id: string, field: string | null) => {
    if (field) {
      setEditingField({ id, field });
    } else {
      setEditingField(null);
    }
  };

  const handleSaveSubtitle = (id: string, updates: Partial<SubtitleItem>) => {
    updateBothSubtitles({ id, updates });
    setEditingField(null);
    toast.success("Subtitle updated");
  };

  const handleDeleteSubtitle = (id: string) => {
    deleteSubtitle(id);
    setEditingField(null);
    toast.success("Subtitle deleted");
  };

  const handleGenerateSubtitles = async () => {
    if (!promptText.trim()) {
      toast.error("Please enter a prompt");
      return;
    }

    if (!currentTask.videoUrl) {
      toast.error("Please select a video first");
      return;
    }

    setIsGenerating(true);

    try {
      // Get language names for the prompt
      const sourceLanguage = currentTask.settings?.sourceLanguage || 'en';
      const targetLanguage = currentTask.settings?.targetLanguage || 'zh';

      // Map language codes to names
      const getLanguageName = (code: string) => {
        const languageMap: Record<string, string> = {
          'en': 'English',
          'zh': 'Chinese',
          'es': 'Spanish',
          'fr': 'French',
          'de': 'German',
          'ja': 'Japanese',
          'ko': 'Korean',
          'pt': 'Portuguese',
          'ru': 'Russian',
          'ar': 'Arabic',
          'hi': 'Hindi',
          'it': 'Italian',
          'nl': 'Dutch',
          'sv': 'Swedish',
          'da': 'Danish',
          'no': 'Norwegian',
          'fi': 'Finnish',
          'pl': 'Polish',
          'tr': 'Turkish',
          'th': 'Thai',
          'vi': 'Vietnamese',
          'id': 'Indonesian',
          'ms': 'Malay',
          'tl': 'Filipino',
          'ur': 'Urdu',
          'bn': 'Bengali',
          'ta': 'Tamil',
          'te': 'Telugu',
          'ml': 'Malayalam',
          'kn': 'Kannada',
          'gu': 'Gujarati',
          'pa': 'Punjabi',
          'or': 'Odia',
          'as': 'Assamese',
          'ne': 'Nepali',
          'si': 'Sinhala',
          'my': 'Myanmar',
          'km': 'Khmer',
          'lo': 'Lao',
          'ka': 'Georgian',
          'am': 'Amharic',
          'sw': 'Swahili',
          'zu': 'Zulu',
          'af': 'Afrikaans',
          'sq': 'Albanian',
          'eu': 'Basque',
          'be': 'Belarusian',
          'bg': 'Bulgarian',
          'ca': 'Catalan',
          'hr': 'Croatian',
          'cs': 'Czech',
          'et': 'Estonian',
          'gl': 'Galician',
          'el': 'Greek',
          'he': 'Hebrew',
          'hu': 'Hungarian',
          'is': 'Icelandic',
          'ga': 'Irish',
          'lv': 'Latvian',
          'lt': 'Lithuanian',
          'mk': 'Macedonian',
          'mt': 'Maltese',
          'ro': 'Romanian',
          'sk': 'Slovak',
          'sl': 'Slovenian',
          'sr': 'Serbian',
          'uk': 'Ukrainian',
          'cy': 'Welsh'
        };
        return languageMap[code] || code.toUpperCase();
      };

      const originalLanguageName = getLanguageName(sourceLanguage);
      const targetLanguageName = getLanguageName(targetLanguage);

      // Prepare the AI prompt for subtitle generation
      const aiPrompt = `Generate subtitles for a video based on this prompt: "${promptText}"

Video details:
- Duration: ${currentTask.duration || videoInfo?.info?.duration || 'unknown'} seconds
- Source language: ${originalLanguageName} (${sourceLanguage})
- Target language: ${targetLanguageName} (${targetLanguage})

Please generate 5-10 subtitle segments in SRT format with both original and translated text. Use this exact format:

1
00:00:01,234 --> 00:00:04,567
This is the original sentence in ${originalLanguageName}.
This is the translated sentence in ${targetLanguageName}.

2
00:00:04,567 --> 00:00:07,890
Another original sentence in ${originalLanguageName}.
Another translated sentence in ${targetLanguageName}.

Make sure:
- Each subtitle has a sequential number
- Timing is realistic and matches the video duration
- Each subtitle block has exactly 2 lines: original text first, then translated text
- Content matches the user's prompt
- Return only the SRT content, no additional text or explanations.`;

      // Call 302.ai API
      const response = await fetch('https://api.302.ai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer sk-gJj2ExFbK8qdFbVgfRQY9FfmpYkte8PGCkI4LCVoBnitvkgx',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: "gemini-1.5-pro",
          messages: [
            {
              role: "user",
              content: aiPrompt
            }
          ]
        }),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const result = await response.json();

      if (result.choices && result.choices[0] && result.choices[0].message) {
        const aiResponse = result.choices[0].message.content;

        try {
          // Parse SRT format response
          const parsedSubtitles = parseSRTContent(aiResponse);

          if (parsedSubtitles.length > 0) {
            setSubtitleData({
              prompt: promptText,
              originalSubtitles: parsedSubtitles,
              translatedSubtitles: parsedSubtitles.map((sub: SubtitleItem) => ({ ...sub, translatedText: sub.translatedText || "" })),
            });

            toast.success("Subtitles generated successfully!");
            setShowPromptInput(false);
            setPromptText("");
            return;
          } else {
            throw new Error("No valid subtitles found in AI response");
          }
        } catch (parseError) {
          console.error("Failed to parse SRT response:", parseError);
          throw new Error("AI response was not in valid SRT format");
        }
      } else {
        throw new Error("Invalid response from AI API");
      }

    } catch (error) {
      console.error("Error generating subtitles:", error);

      // Fallback to mock data if API fails
      const sourceLanguage = currentTask.settings?.sourceLanguage || 'en';
      const targetLanguage = currentTask.settings?.targetLanguage || 'zh';

      const mockSubtitles = [
        {
          id: "1",
          startTime: "00:00:00,000",
          endTime: "00:00:03,000",
          text: "Welcome to our tutorial",
          translatedText: sourceLanguage !== targetLanguage ? "欢迎来到我们的教程" : "",
        },
        {
          id: "2",
          startTime: "00:00:03,000",
          endTime: "00:00:06,000",
          text: "Today we'll learn something new",
          translatedText: sourceLanguage !== targetLanguage ? "今天我们将学习新的内容" : "",
        },
        {
          id: "3",
          startTime: "00:00:06,000",
          endTime: "00:00:09,000",
          text: "Let's get started with the basics",
          translatedText: sourceLanguage !== targetLanguage ? "让我们从基础开始" : "",
        },
      ];

      setSubtitleData({
        prompt: promptText,
        originalSubtitles: mockSubtitles,
        translatedSubtitles: mockSubtitles.map((sub: SubtitleItem) => ({ ...sub, translatedText: sub.translatedText || "" })),
      });

      toast.success("Subtitles generated (using fallback data)");
      setShowPromptInput(false);
      setPromptText("");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSeekTo = (time: number) => {
    // Set the current time for subtitle sync
    setCurrentTime(time);
    // Note: In a real implementation, this would seek the video player
    toast.success(`Seeking to ${time.toFixed(1)}s`);
  };

  const handleExportSubtitles = () => {
    const srtContent = subtitleData.originalSubtitles
      .map((sub, index) => {
        return `${index + 1}\n${sub.startTime.replace(',', ' --> ')}${sub.endTime}\n${sub.text}\n`;
      })
      .join('\n');

    const blob = new Blob([srtContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'subtitles.srt';
    a.click();
    URL.revokeObjectURL(url);

    toast.success("Subtitles exported");
  };

  // Get current active subtitle based on video time
  const getCurrentSubtitle = () => {
    return subtitleData.originalSubtitles.find(subtitle => {
      const startTime = timeStringToSeconds(subtitle.startTime);
      const endTime = timeStringToSeconds(subtitle.endTime);
      return currentTime >= startTime && currentTime <= endTime;
    });
  };

  const currentSubtitle = getCurrentSubtitle();

  // Note: Video player events are now handled by the VideoPreview component
  // We'll simulate time updates for demonstration purposes
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate video time progression for subtitle sync demo
      if (isPlaying) {
        setCurrentTime(prev => prev + 0.1);
      }
    }, 100);

    return () => clearInterval(interval);
  }, [isPlaying]);

  return (
    <>
      {/* Header */}
      <div className="flex items-center gap-3 border-b bg-card/50 p-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="h-7 w-7 p-0"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex items-center gap-2 flex-1">
          <Edit3 className="h-4 w-4 text-primary" />
          <h2 className="text-sm font-medium">
            {t("form.fields.subtitleEditor.title")} with Video Preview
          </h2>
        </div>
      </div>

      {/* Main Content - Split Layout */}
      <div className="flex-1 flex">
        {/* Video Preview Panel */}
        <div className="w-1/2 border-r flex flex-col">
          {/* Video Player */}
          <div className="p-4">
            <div className="overflow-hidden rounded-lg border bg-card">
              {(() => {
                const isUploadedVideo = !!(
                  currentTask.videoUrl &&
                  currentTask.thumbnail &&
                  currentTask.name &&
                  currentTask.duration
                );

                if (isUploadedVideo) {
                  return (
                    <div className="relative">
                      <VideoPreview
                        thumbnail={currentTask.thumbnail!}
                        title={currentTask.name!}
                        url={currentTask.videoUrl!}
                        duration={currentTask.duration!}
                        subtitleStyle={currentTask.settings?.subtitleStyle}
                        subtitleLayout={currentTask.settings?.subtitleLayout}
                        showSubtitle={false} // Disable default subtitle preview
                        sourceLanguage={currentTask.settings?.sourceLanguage}
                        targetLanguage={currentTask.settings?.targetLanguage}
                      />
                      {/* Custom subtitle overlay with actual subtitle content */}
                      {currentSubtitle && (
                        <CustomSubtitleOverlay
                          subtitle={currentSubtitle}
                          style={currentTask.settings?.subtitleStyle}
                          layout={currentTask.settings?.subtitleLayout}
                        />
                      )}
                    </div>
                  );
                } else if (videoInfo?.info) {
                  return (
                    <div className="relative">
                      <VideoPreview
                        thumbnail={videoInfo.info.thumbnail}
                        title={videoInfo.info.title}
                        url={
                          currentTask.videoUrl && isYoutubeUrl(currentTask.videoUrl)
                            ? currentTask.videoUrl
                            : currentTask.settings?.selectedFormat?.url || ""
                        }
                        duration={videoInfo.info.duration}
                        subtitleStyle={currentTask.settings?.subtitleStyle}
                        subtitleLayout={currentTask.settings?.subtitleLayout}
                        showSubtitle={false} // Disable default subtitle preview
                        sourceLanguage={currentTask.settings?.sourceLanguage}
                        targetLanguage={currentTask.settings?.targetLanguage}
                      />
                      {/* Custom subtitle overlay with actual subtitle content */}
                      {currentSubtitle && (
                        <CustomSubtitleOverlay
                          subtitle={currentSubtitle}
                          style={currentTask.settings?.subtitleStyle}
                          layout={currentTask.settings?.subtitleLayout}
                        />
                      )}
                    </div>
                  );
                } else if (currentTask.videoUrl) {
                  return (
                    <div className="aspect-video bg-muted flex items-center justify-center">
                      <div className="text-center space-y-2">
                        <div className="text-sm font-medium">Loading video...</div>
                        <div className="text-xs text-muted-foreground">Please wait</div>
                      </div>
                    </div>
                  );
                } else {
                  return (
                    <div className="aspect-video bg-muted flex items-center justify-center">
                      <div className="text-center space-y-2">
                        <div className="text-sm font-medium">No video loaded</div>
                        <div className="text-xs text-muted-foreground">Please select a video from upload or online video first</div>
                      </div>
                    </div>
                  );
                }
              })()}
            </div>
          </div>

          {/* Video Controls Info */}
          <div className="p-4 border-t bg-muted/30">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-4">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsPlaying(!isPlaying)}
                >
                  {isPlaying ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
                </Button>
                <span>Time: {currentTime.toFixed(1)}s</span>
                {currentSubtitle && (
                  <Badge variant="secondary">
                    Active: {timeStringToSeconds(currentSubtitle.startTime).toFixed(1)}s - {timeStringToSeconds(currentSubtitle.endTime).toFixed(1)}s
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Button size="sm" onClick={handleAddSubtitle}>
                  <Plus className="h-3 w-3 mr-1" />
                  Add at {currentTime.toFixed(1)}s
                </Button>
              </div>
            </div>


          </div>
        </div>

        {/* Subtitle Editor Panel */}
        <div className="w-1/2 flex flex-col">
          {/* Action Bar */}
          <div className="flex items-center gap-2 border-b bg-muted/30 p-3">
            <Button
              size="sm"
              variant="default"
              onClick={() => setShowPromptInput(!showPromptInput)}
              disabled={isGenerating}
            >
              <Wand2 className="h-3 w-3 mr-1" />
              Create with AI
            </Button>
            <Button size="sm" onClick={handleAddSubtitle}>
              <Plus className="h-3 w-3 mr-1" />
              {t("form.fields.subtitleEditor.addSubtitle")}
            </Button>
            <Button size="sm" variant="outline" onClick={handleExportSubtitles}>
              <Download className="h-3 w-3 mr-1" />
              {t("form.fields.subtitleEditor.exportSubtitles")}
            </Button>
            <div className="ml-auto text-xs text-muted-foreground">
              {subtitleData.originalSubtitles.length} subtitles
            </div>
          </div>

          {/* AI Prompt Input */}
          {showPromptInput && (
            <div className="border-b bg-card p-4 space-y-3">
              <div className="space-y-2">
                <label className="text-sm font-medium">AI Prompt</label>
                <Textarea
                  value={promptText}
                  onChange={(e) => setPromptText(e.target.value)}
                  placeholder="Enter a prompt to generate subtitles (e.g., 'Create subtitles for a cooking tutorial about making pasta')"
                  className="min-h-[80px] resize-none"
                  disabled={isGenerating}
                />
              </div>
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  onClick={handleGenerateSubtitles}
                  disabled={isGenerating || !promptText.trim()}
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-3 w-3 mr-1" />
                      Generate Subtitles
                    </>
                  )}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setShowPromptInput(false);
                    setPromptText("");
                  }}
                  disabled={isGenerating}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}

          {/* Content */}
          {subtitleData.originalSubtitles.length === 0 ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center space-y-3">
                <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                  <Edit3 className="h-6 w-6 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="font-medium">{t("form.fields.subtitleEditor.noSubtitles")}</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Click "Add Subtitle" to create your first subtitle.
                  </p>
                </div>
                <Button onClick={handleAddSubtitle}>
                  <Plus className="h-4 w-4 mr-2" />
                  {t("form.fields.subtitleEditor.addSubtitle")}
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex-1 flex flex-col">
              {/* Table Header */}
              <div className="grid grid-cols-6 gap-2 p-3 border-b bg-muted/50 font-medium text-sm">
                <div>{t("form.fields.subtitleEditor.timeStart")}</div>
                <div>{t("form.fields.subtitleEditor.timeEnd")}</div>
                <div>{t("form.fields.subtitleEditor.originalSubtitles")}</div>
                <div>{t("form.fields.subtitleEditor.translatedSubtitles")}</div>
                <div>Duration</div>
                <div>Actions</div>
              </div>

              {/* Table Content */}
              <ScrollArea className="flex-1">
                <div>
                  {subtitleData.originalSubtitles.map((subtitle) => {
                    // Find the corresponding translated subtitle
                    const translatedSubtitle = subtitleData.translatedSubtitles.find(t => t.id === subtitle.id);
                    const combinedSubtitle = {
                      ...subtitle,
                      translatedText: translatedSubtitle?.translatedText || ""
                    };

                    return (
                      <SubtitleRow
                        key={subtitle.id}
                        subtitle={combinedSubtitle}
                        editingField={editingField?.id === subtitle.id ? editingField.field : null}
                        onFieldEdit={(field) => handleFieldEdit(subtitle.id, field)}
                        onSave={(updates) => handleSaveSubtitle(subtitle.id, updates)}
                        onDelete={() => handleDeleteSubtitle(subtitle.id)}
                        isActive={currentSubtitle?.id === subtitle.id}
                        onSeekTo={handleSeekTo}
                      />
                    );
                  })}
                </div>
              </ScrollArea>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
