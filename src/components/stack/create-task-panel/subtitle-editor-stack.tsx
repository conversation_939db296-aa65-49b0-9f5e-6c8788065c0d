"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { StackRef } from "@/components/ui/stack";
import { ArrowLeft, Edit3, Eye, Plus, Save, Download, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { RefObject, useState } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import {
  subtitleDataAtom,
  subtitleEditingModeAtom,
  editingSubtitleIdAtom,
  addSubtitleAtom,
  updateBothSubtitlesAtom,
  deleteSubtitleAtom,
  SubtitleItem
} from "@/stores/slices/subtitle_store";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface SubtitleEditorStackProps {
  stackRef: RefObject<StackRef>;
}

interface SubtitleRowProps {
  subtitle: SubtitleItem;
  isEditing: boolean;
  onEdit: () => void;
  onSave: (updates: Partial<SubtitleItem>) => void;
  onDelete: () => void;
}

const SubtitleRow = ({
  subtitle,
  isEditing,
  onEdit,
  onSave,
  onDelete
}: SubtitleRowProps) => {
  const t = useTranslations();
  const [editData, setEditData] = useState({
    startTime: subtitle.startTime,
    endTime: subtitle.endTime,
    text: subtitle.text,
    translatedText: subtitle.translatedText || "",
  });

  const handleSave = () => {
    onSave({
      startTime: editData.startTime,
      endTime: editData.endTime,
      text: editData.text,
      translatedText: editData.translatedText,
    });
  };

  const handleCancel = () => {
    setEditData({
      startTime: subtitle.startTime,
      endTime: subtitle.endTime,
      text: subtitle.text,
      translatedText: subtitle.translatedText || "",
    });
    onEdit(); // This will set isEditing to false
  };

  return (
    <div className={cn(
      "grid grid-cols-5 gap-2 p-3 border-b hover:bg-muted/30 transition-colors",
      isEditing && "bg-primary/5 border-primary/20"
    )}>
      {/* Start Time */}
      <div className="flex flex-col gap-1">
        {isEditing ? (
          <Input
            value={editData.startTime}
            onChange={(e) => setEditData(prev => ({ ...prev, startTime: e.target.value }))}
            className="h-8 text-xs"
            placeholder="00:00:00,000"
          />
        ) : (
          <div className="text-xs font-mono bg-muted/50 px-2 py-1 rounded">
            {subtitle.startTime}
          </div>
        )}
      </div>

      {/* End Time */}
      <div className="flex flex-col gap-1">
        {isEditing ? (
          <Input
            value={editData.endTime}
            onChange={(e) => setEditData(prev => ({ ...prev, endTime: e.target.value }))}
            className="h-8 text-xs"
            placeholder="00:00:03,000"
          />
        ) : (
          <div className="text-xs font-mono bg-muted/50 px-2 py-1 rounded">
            {subtitle.endTime}
          </div>
        )}
      </div>

      {/* Original Text */}
      <div className="flex flex-col gap-1">
        {isEditing ? (
          <Textarea
            value={editData.text}
            onChange={(e) => setEditData(prev => ({ ...prev, text: e.target.value }))}
            className="min-h-[60px] text-sm resize-none"
            placeholder="Enter subtitle text..."
          />
        ) : (
          <div className="text-sm leading-relaxed p-2 min-h-[60px] bg-background border rounded">
            {subtitle.text}
          </div>
        )}
      </div>

      {/* Translated Text */}
      <div className="flex flex-col gap-1">
        {isEditing ? (
          <Textarea
            value={editData.translatedText}
            onChange={(e) => setEditData(prev => ({ ...prev, translatedText: e.target.value }))}
            className="min-h-[60px] text-sm resize-none"
            placeholder="Enter translation..."
          />
        ) : (
          <div className="text-sm leading-relaxed p-2 min-h-[60px] bg-background border rounded">
            {subtitle.translatedText || (
              <span className="text-muted-foreground italic">No translation yet...</span>
            )}
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-start gap-1">
        {isEditing ? (
          <div className="flex flex-col gap-1">
            <Button size="sm" onClick={handleSave} className="h-7">
              <Save className="h-3 w-3 mr-1" />
              Save
            </Button>
            <Button size="sm" variant="ghost" onClick={handleCancel} className="h-7">
              Cancel
            </Button>
          </div>
        ) : (
          <div className="flex flex-col gap-1">
            <Button size="sm" variant="ghost" onClick={onEdit} className="h-7">
              <Edit3 className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="ghost" onClick={onDelete} className="h-7">
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export const SubtitleEditorStack = ({ stackRef }: SubtitleEditorStackProps) => {
  const t = useTranslations();
  const subtitleData = useAtomValue(subtitleDataAtom);
  const [editingMode, setEditingMode] = useAtom(subtitleEditingModeAtom);
  const [editingId, setEditingId] = useAtom(editingSubtitleIdAtom);
  const addSubtitle = useSetAtom(addSubtitleAtom);
  const updateBothSubtitles = useSetAtom(updateBothSubtitlesAtom);
  const deleteSubtitle = useSetAtom(deleteSubtitleAtom);

  const handleBack = () => {
    stackRef.current?.pop();
  };

  const handleAddSubtitle = () => {
    const lastSubtitle = subtitleData.originalSubtitles[subtitleData.originalSubtitles.length - 1];
    const startTime = lastSubtitle ? lastSubtitle.endTime : "00:00:00,000";
    
    addSubtitle({
      startTime,
      endTime: "00:00:03,000",
      text: "New subtitle text",
    });
    
    toast.success("New subtitle added");
  };

  const handleEditSubtitle = (id: string) => {
    setEditingId(editingId === id ? null : id);
  };

  const handleSaveSubtitle = (id: string, updates: Partial<SubtitleItem>) => {
    // Update both original and translated data
    updateBothSubtitles({ id, updates });
    setEditingId(null);
    toast.success("Subtitle updated");
  };

  const handleDeleteSubtitle = (id: string) => {
    deleteSubtitle(id);
    toast.success("Subtitle deleted");
  };

  const handleExportSubtitles = () => {
    // Simple SRT export
    const srtContent = subtitleData.originalSubtitles
      .map((sub, index) => {
        return `${index + 1}\n${sub.startTime.replace(',', ' --> ')}${sub.endTime}\n${sub.text}\n`;
      })
      .join('\n');
    
    const blob = new Blob([srtContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'subtitles.srt';
    a.click();
    URL.revokeObjectURL(url);
    
    toast.success("Subtitles exported");
  };

  return (
    <>
      {/* Header */}
      <div className="flex items-center gap-3 border-b bg-card/50 p-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="h-7 w-7 p-0"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex items-center gap-2 flex-1">
          <Edit3 className="h-4 w-4 text-primary" />
          <h2 className="text-sm font-medium">
            {t("form.fields.subtitleEditor.title")}
          </h2>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={editingMode === "editing" ? "default" : "outline"}
            size="sm"
            onClick={() => setEditingMode("editing")}
          >
            <Edit3 className="h-3 w-3 mr-1" />
            {t("form.fields.subtitleEditor.editingMode")}
          </Button>
          <Button
            variant={editingMode === "preview" ? "default" : "outline"}
            size="sm"
            onClick={() => setEditingMode("preview")}
          >
            <Eye className="h-3 w-3 mr-1" />
            {t("form.fields.subtitleEditor.previewMode")}
          </Button>
        </div>
      </div>

      {/* Action Bar */}
      <div className="flex items-center gap-2 border-b bg-muted/30 p-3">
        <Button size="sm" onClick={handleAddSubtitle}>
          <Plus className="h-3 w-3 mr-1" />
          {t("form.fields.subtitleEditor.addSubtitle")}
        </Button>
        <Button size="sm" variant="outline" onClick={handleExportSubtitles}>
          <Download className="h-3 w-3 mr-1" />
          {t("form.fields.subtitleEditor.exportSubtitles")}
        </Button>
        <div className="ml-auto text-xs text-muted-foreground">
          {subtitleData.originalSubtitles.length} subtitles
        </div>
      </div>

      {/* Content */}
      {subtitleData.originalSubtitles.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center space-y-3">
            <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
              <Edit3 className="h-6 w-6 text-muted-foreground" />
            </div>
            <div>
              <h3 className="font-medium">{t("form.fields.subtitleEditor.noSubtitles")}</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Click "Add Subtitle" to create your first subtitle.
              </p>
            </div>
            <Button onClick={handleAddSubtitle}>
              <Plus className="h-4 w-4 mr-2" />
              {t("form.fields.subtitleEditor.addSubtitle")}
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex flex-col">
          {/* Table Header */}
          <div className="grid grid-cols-5 gap-2 p-3 border-b bg-muted/50 font-medium text-sm">
            <div>{t("form.fields.subtitleEditor.timeStart")}</div>
            <div>{t("form.fields.subtitleEditor.timeEnd")}</div>
            <div>{t("form.fields.subtitleEditor.originalSubtitles")}</div>
            <div>{t("form.fields.subtitleEditor.translatedSubtitles")}</div>
            <div>Actions</div>
          </div>

          {/* Table Content */}
          <ScrollArea className="flex-1">
            <div>
              {subtitleData.originalSubtitles.map((subtitle) => {
                // Find the corresponding translated subtitle
                const translatedSubtitle = subtitleData.translatedSubtitles.find(t => t.id === subtitle.id);
                const combinedSubtitle = {
                  ...subtitle,
                  translatedText: translatedSubtitle?.translatedText || ""
                };

                return (
                  <SubtitleRow
                    key={subtitle.id}
                    subtitle={combinedSubtitle}
                    isEditing={editingId === subtitle.id}
                    onEdit={() => handleEditSubtitle(subtitle.id)}
                    onSave={(updates) => handleSaveSubtitle(subtitle.id, updates)}
                    onDelete={() => handleDeleteSubtitle(subtitle.id)}
                  />
                );
              })}
            </div>
          </ScrollArea>
        </div>
      )}
    </>
  );
};
