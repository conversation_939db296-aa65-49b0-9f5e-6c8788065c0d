"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { StackRef } from "@/components/ui/stack";
import { ArrowLeft, Edit3, Eye, Plus, Save, Download, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { RefObject, useState } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { 
  subtitleDataAtom, 
  subtitleEditingModeAtom,
  editingSubtitleIdAtom,
  addSubtitleAtom,
  updateSubtitleAtom,
  deleteSubtitleAtom,
  SubtitleItem
} from "@/stores/slices/subtitle_store";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface SubtitleEditorStackProps {
  stackRef: RefObject<StackRef>;
}

interface SubtitleItemEditorProps {
  subtitle: SubtitleItem;
  type: "original" | "translated";
  isEditing: boolean;
  onEdit: () => void;
  onSave: (updates: Partial<SubtitleItem>) => void;
  onDelete: () => void;
}

const SubtitleItemEditor = ({ 
  subtitle, 
  type, 
  isEditing, 
  onEdit, 
  onSave, 
  onDelete 
}: SubtitleItemEditorProps) => {
  const t = useTranslations();
  const [editData, setEditData] = useState({
    startTime: subtitle.startTime,
    endTime: subtitle.endTime,
    text: type === "original" ? subtitle.text : (subtitle.translatedText || ""),
  });

  const handleSave = () => {
    if (type === "original") {
      onSave({
        startTime: editData.startTime,
        endTime: editData.endTime,
        text: editData.text,
      });
    } else {
      onSave({
        translatedText: editData.text,
      });
    }
  };

  const handleCancel = () => {
    setEditData({
      startTime: subtitle.startTime,
      endTime: subtitle.endTime,
      text: type === "original" ? subtitle.text : (subtitle.translatedText || ""),
    });
    onEdit(); // This will set isEditing to false
  };

  return (
    <Card className={cn("transition-all", isEditing && "ring-2 ring-primary")}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {subtitle.startTime} → {subtitle.endTime}
            </Badge>
          </div>
          <div className="flex items-center gap-1">
            {isEditing ? (
              <>
                <Button size="sm" variant="ghost" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button size="sm" onClick={handleSave}>
                  <Save className="h-3 w-3 mr-1" />
                  Save
                </Button>
              </>
            ) : (
              <>
                <Button size="sm" variant="ghost" onClick={onEdit}>
                  <Edit3 className="h-3 w-3" />
                </Button>
                {type === "original" && (
                  <Button size="sm" variant="ghost" onClick={onDelete}>
                    <Trash2 className="h-3 w-3" />
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {isEditing ? (
          <div className="space-y-3">
            {type === "original" && (
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">{t("form.fields.subtitleEditor.timeStart")}</Label>
                  <Input
                    value={editData.startTime}
                    onChange={(e) => setEditData(prev => ({ ...prev, startTime: e.target.value }))}
                    className="h-8 text-xs"
                    placeholder="00:00:00,000"
                  />
                </div>
                <div>
                  <Label className="text-xs">{t("form.fields.subtitleEditor.timeEnd")}</Label>
                  <Input
                    value={editData.endTime}
                    onChange={(e) => setEditData(prev => ({ ...prev, endTime: e.target.value }))}
                    className="h-8 text-xs"
                    placeholder="00:00:03,000"
                  />
                </div>
              </div>
            )}
            <div>
              <Label className="text-xs">{t("form.fields.subtitleEditor.subtitleText")}</Label>
              <Textarea
                value={editData.text}
                onChange={(e) => setEditData(prev => ({ ...prev, text: e.target.value }))}
                className="min-h-[60px] text-sm"
                placeholder={type === "original" ? "Enter subtitle text..." : "Enter translation..."}
              />
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <p className="text-sm leading-relaxed">
              {type === "original" ? subtitle.text : (subtitle.translatedText || (
                <span className="text-muted-foreground italic">No translation yet...</span>
              ))}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export const SubtitleEditorStack = ({ stackRef }: SubtitleEditorStackProps) => {
  const t = useTranslations();
  const subtitleData = useAtomValue(subtitleDataAtom);
  const [editingMode, setEditingMode] = useAtom(subtitleEditingModeAtom);
  const [editingId, setEditingId] = useAtom(editingSubtitleIdAtom);
  const addSubtitle = useSetAtom(addSubtitleAtom);
  const updateSubtitle = useSetAtom(updateSubtitleAtom);
  const deleteSubtitle = useSetAtom(deleteSubtitleAtom);

  const handleBack = () => {
    stackRef.current?.pop();
  };

  const handleAddSubtitle = () => {
    const lastSubtitle = subtitleData.originalSubtitles[subtitleData.originalSubtitles.length - 1];
    const startTime = lastSubtitle ? lastSubtitle.endTime : "00:00:00,000";
    
    addSubtitle({
      startTime,
      endTime: "00:00:03,000",
      text: "New subtitle text",
    });
    
    toast.success("New subtitle added");
  };

  const handleEditSubtitle = (id: string) => {
    setEditingId(editingId === id ? null : id);
  };

  const handleSaveSubtitle = (id: string, updates: Partial<SubtitleItem>, type: "original" | "translated") => {
    updateSubtitle({ id, updates, type });
    setEditingId(null);
    toast.success("Subtitle updated");
  };

  const handleDeleteSubtitle = (id: string) => {
    deleteSubtitle(id);
    toast.success("Subtitle deleted");
  };

  const handleExportSubtitles = () => {
    // Simple SRT export
    const srtContent = subtitleData.originalSubtitles
      .map((sub, index) => {
        return `${index + 1}\n${sub.startTime.replace(',', ' --> ')}${sub.endTime}\n${sub.text}\n`;
      })
      .join('\n');
    
    const blob = new Blob([srtContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'subtitles.srt';
    a.click();
    URL.revokeObjectURL(url);
    
    toast.success("Subtitles exported");
  };

  return (
    <div className="flex h-full flex-col">
      {/* Header */}
      <div className="flex items-center gap-3 border-b bg-card/50 p-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="h-7 w-7 p-0"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex items-center gap-2 flex-1">
          <Edit3 className="h-4 w-4 text-primary" />
          <h2 className="text-sm font-medium">
            {t("form.fields.subtitleEditor.title")}
          </h2>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={editingMode === "editing" ? "default" : "outline"}
            size="sm"
            onClick={() => setEditingMode("editing")}
          >
            <Edit3 className="h-3 w-3 mr-1" />
            {t("form.fields.subtitleEditor.editingMode")}
          </Button>
          <Button
            variant={editingMode === "preview" ? "default" : "outline"}
            size="sm"
            onClick={() => setEditingMode("preview")}
          >
            <Eye className="h-3 w-3 mr-1" />
            {t("form.fields.subtitleEditor.previewMode")}
          </Button>
        </div>
      </div>

      {/* Action Bar */}
      <div className="flex items-center gap-2 border-b bg-muted/30 p-3">
        <Button size="sm" onClick={handleAddSubtitle}>
          <Plus className="h-3 w-3 mr-1" />
          {t("form.fields.subtitleEditor.addSubtitle")}
        </Button>
        <Button size="sm" variant="outline" onClick={handleExportSubtitles}>
          <Download className="h-3 w-3 mr-1" />
          {t("form.fields.subtitleEditor.exportSubtitles")}
        </Button>
        <div className="ml-auto text-xs text-muted-foreground">
          {subtitleData.originalSubtitles.length} subtitles
        </div>
      </div>

      {/* Content */}
      {subtitleData.originalSubtitles.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center space-y-3">
            <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
              <Edit3 className="h-6 w-6 text-muted-foreground" />
            </div>
            <div>
              <h3 className="font-medium">{t("form.fields.subtitleEditor.noSubtitles")}</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Click "Add Subtitle" to create your first subtitle.
              </p>
            </div>
            <Button onClick={handleAddSubtitle}>
              <Plus className="h-4 w-4 mr-2" />
              {t("form.fields.subtitleEditor.addSubtitle")}
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex-1 grid grid-cols-2 gap-4 p-4">
          {/* Original Subtitles */}
          <div className="space-y-4">
            <h3 className="font-medium text-sm flex items-center gap-2">
              <Badge variant="secondary">Original</Badge>
              {t("form.fields.subtitleEditor.originalSubtitles")}
            </h3>
            <ScrollArea className="h-[calc(100vh-200px)]">
              <div className="space-y-3 pr-4">
                {subtitleData.originalSubtitles.map((subtitle) => (
                  <SubtitleItemEditor
                    key={subtitle.id}
                    subtitle={subtitle}
                    type="original"
                    isEditing={editingId === subtitle.id}
                    onEdit={() => handleEditSubtitle(subtitle.id)}
                    onSave={(updates) => handleSaveSubtitle(subtitle.id, updates, "original")}
                    onDelete={() => handleDeleteSubtitle(subtitle.id)}
                  />
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Translated Subtitles */}
          <div className="space-y-4">
            <h3 className="font-medium text-sm flex items-center gap-2">
              <Badge variant="secondary">Translated</Badge>
              {t("form.fields.subtitleEditor.translatedSubtitles")}
            </h3>
            <ScrollArea className="h-[calc(100vh-200px)]">
              <div className="space-y-3 pr-4">
                {subtitleData.translatedSubtitles.map((subtitle) => (
                  <SubtitleItemEditor
                    key={subtitle.id}
                    subtitle={subtitle}
                    type="translated"
                    isEditing={editingId === subtitle.id}
                    onEdit={() => handleEditSubtitle(subtitle.id)}
                    onSave={(updates) => handleSaveSubtitle(subtitle.id, updates, "translated")}
                    onDelete={() => {}} // No delete for translated subtitles
                  />
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
      )}
    </div>
  );
};
